#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
涨停侦探 (ZtDetective) v1.0
- 结果驱动：从收盘涨停股名单出发，回溯全天数据。
- 时间线索：为每只涨停股建立详细的异动时间线档案。
- 模式识别：量化关键信号，识别多种涨停模式。
- 深度报告：生成结构化、有洞察的Markdown分析报告。
"""

import os
import pandas as pd
import re
from collections import defaultdict
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# --- 配置区 ---
ANALYSIS_DATE = "2025-08-01"
FUND_DATA_DIR = "../fund_data"  # 假设数据在 fund_data/YYYY-MM-DD/ 目录下
POST_MARKET_DATA_DIR = "D:\\stock_dev\\zt_any\\data"
OUTPUT_DIR = "../reports"


# --- 配置区结束 ---

class ZtDetective:
    def __init__(self, analysis_date, data_dir, post_market_data_dir):
        self.analysis_date = analysis_date
        # --- 今日数据路径 ---
        self.data_dir = os.path.join(data_dir, analysis_date)
        self.post_market_data_dir = os.path.join(post_market_data_dir, analysis_date.replace('-', ''))

        # --- 新增：昨日数据路径 ---
        yesterday = datetime.strptime(analysis_date, '%Y-%m-%d') - timedelta(days=1)
        self.yesterday_str_hyphen = yesterday.strftime('%Y-%m-%d')
        yesterday_str_numeric = yesterday.strftime('%Y%m%d')
        self.yesterday_data_dir = os.path.join(data_dir, self.yesterday_str_hyphen)
        self.yesterday_post_market_data_dir = os.path.join(post_market_data_dir, yesterday_str_numeric)

        # --- 数据存储 ---
        self.all_day_data = {}
        self.post_market_data = {}
        self.stock_info_map = {}
        self.yesterday_data = {'intraday': defaultdict(list), 'post_market': {}}  # 新增：存储昨日数据

        self.file_types = {
            'fund_flow': ['fund_flow_tpdog.csv', 'ths_fund_flow.csv', 'fund_flow_akshare.csv', 'fund_flow_ths.csv'],
            'concept_flow': ['concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv', 'concept_fund_flow_', '实时概念资金流_'],
            'sector_flow': ['sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv', 'sector_fund_flow_rank_'],
            'zt_pool': [
                'zt_pool.csv', 'previous_zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_', 'limit_up_pool_',
                'zt_pool_', '_zt_pool.csv'  # 支持 zt_pool_20250730_150228.csv 和 14-57_zt_pool.csv 格式
            ],
            'zt_pool_previous': [
                'zt_pool_previous_', 'previous_zt_pool'  # 昨天涨停股池
            ],
            'dt_pool': [
                'dt_pool_', 'dt_pool.csv', '跌停股池_'  # 跌停股池
            ],
            'zb_pool': [
                'zb_pool_', 'zb_pool.csv', '炸板股池_akshare_东方财富_', '炸板股池_'  # 炸板股池
            ],
            'big_purchase': [
                'big_purchase_unusual_', 'big_purchase_', '大笔买入_unusual_', '大笔买入_'  # 大笔买入
            ],
            'big_buy': [
                'big_buy_unusual_', 'big_buy_', '大买盘_unusual_', '大买盘_', '有大买盘_'  # 大买盘
            ],
            'news': ['news_cls.csv', 'news_em.csv', 'news_ths.csv'],
            'index': ['index_sh000001.csv', 'index_sz399006.csv'],
            'movers': ['movers_大笔买入.csv', 'movers_有大买盘.csv'],
            'big_deal': ['ths_big_deal.csv', 'big_deal_'],
            'board_changes': ['board_changes.csv'],
            'market_flow': ['market_fund_flow.csv', 'market_fund_flow_'],
            'industry_board': ['industry_board_ths.csv', 'industry_board_em.csv', 'industry_board_akshare.csv'],
            'indicator': ['indicator_创月新高.csv'],
            'individual_flow': ['individual_fund_flow_', '股票资金流_zssh_', '股票资金流_zssz_', '股票资金流_zsbj_'],
            'lhb': ['lhb_jgmmtj.csv'],
            'dzjy': ['dzjy_mrmx.csv'],
            'notices': ['stock_notices.txt'],
            'sector_summary': ['sector_summary_'],
            'acceleration_signals': ['acceleration_signals_'],
            'fund_flow_rank': ['fund_flow_rank_'],
            'main_fund_flow': ['main_fund_flow_'],
            'stock_notifications': ['stock_notifications.csv'],
            'stock_signals': ['stock_signals.csv']
        }

    def run_analysis(self):
        """
        主分析流程，执行所有步骤并生成报告。
        """
        print("🕵️‍♂️ 涨停侦探开始工作...")

        # 1. 加载全天时间线数据到内存
        print(f"步骤 1/8: 正在加载 {self.analysis_date} 的全天时间线数据...")
        if not self._load_all_day_data():
            print(f"❌ 错误: 时间线数据加载失败，请检查 '{self.data_dir}' 目录。")
            return

        # 2. 加载盘后静态数据
        print(f"步骤 2/8: 正在加载 {self.analysis_date} 的盘后静态数据...")
        self._load_post_market_data()

        # 3. 加载昨日数据
        print(f"步骤 3/8: 正在加载昨日 ({self.yesterday_str_hyphen}) 的关联数据...")
        self._load_yesterday_data()

        # 4. 确定最终涨停股名单（案件卷宗）
        print("步骤 4/8: 正在确定最终涨停股名单...")
        target_stocks = self._get_target_list()
        if not target_stocks:
            print("❌ 错误: 未能找到收盘后的涨停股名单。")
            return
        print(f"  - 锁定 {len(target_stocks)} 只最终涨停股。")

        # 5. 构建包含完整信息的股票映射表
        print("步骤 5/8: 正在构建股票信息映射表...")
        self._build_stock_info_map(target_stocks)

        # 6. 为每只涨停股建立时间线档案
        print("步骤 6/8: 正在为每只涨停股建立回溯档案...")
        archives = [self._build_timeline_archive(stock) for stock in target_stocks]

        # 7. 进行信号量化和模式识别
        print("步骤 7/8: 正在进行信号量化与涨停模式识别...")
        signal_stats = self._quantify_signals(archives)
        archives = self._classify_patterns(archives)

        # 8. 生成最终的深度分析报告
        print("步骤 8/8: 正在生成深度分析报告...")
        report_content = self._generate_report(archives, signal_stats, target_stocks)
        self._save_report(report_content)

        print("\n✅ 侦查工作完成！报告已生成。")

    def _load_all_day_data(self):
        """
        一次性加载当天所有时间戳的数据文件到内存中，按时间点和类型组织。
        """
        if not os.path.exists(self.data_dir):
            return False

        for filename in os.listdir(self.data_dir):
            # 提取时间戳
            time_key = self._extract_time_key(filename)
            if not time_key: continue

            file_type = self._classify_file(filename)
            if file_type == 'other': continue

            if time_key not in self.all_day_data:
                self.all_day_data[time_key] = defaultdict(list)

            filepath = os.path.join(self.data_dir, filename)
            df = self._load_csv_safe(filepath)
            if df is not None:
                self.all_day_data[time_key][file_type].append(df)

        return bool(self.all_day_data)

    def _load_post_market_data(self):
        """
        加载由 stock_data_collector_real.py 生成的盘后静态数据。
        """
        # 定义需要加载的盘后文件名和对应的key
        post_market_files = {
            'zt_pool': '涨停股池.csv', # 核心新增：加载最终涨停股池文件
            'lhb_detail': '龙虎榜详情_当日.csv',
            'cxg': '创新高_前100.csv',
            'lxsz': '连续上涨_全部.csv',
            'cxfl': '持续放量_前100.csv',
            'ljqs': '量价齐升_全部.csv',
            'xstp_5ma': '向上突破_5MA.csv',
            'xstp_10ma': '向上突破_10MA.csv',
            'xstp_20ma': '向上突破_20MA.csv',
            'xstp_30ma': '向上突破_30MA.csv',
            'xstp_60ma': '向上突破_60MA.csv',
            'stock_news': '涨停股池个股新闻.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{self.analysis_date.replace("-", "")}.csv',
            'performance_report': f'业绩报表_{self.analysis_date.replace("-", "")[0:4]}0630.csv', # 假设是中报季
            'performance_forecast': f'业绩预告_{self.analysis_date.replace("-", "")[0:4]}0630.csv',
        }

        # 检查盘后数据目录是否存在
        if not os.path.exists(self.post_market_data_dir):
            print(f"  - 警告: 盘后数据目录未找到: {self.post_market_data_dir}")
            return

        for key, filename in post_market_files.items():
            filepath = os.path.join(self.post_market_data_dir, filename)
            if os.path.exists(filepath):
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.post_market_data[key] = df
                    # print(f"  - 已加载盘后数据: {filename}") # 注释掉以保持日志简洁
            else:
                # 兼容不同季度的财报文件名
                if '业绩' in key:
                    for quarter in ['0331', '0930', '1231']:
                         alt_filename = filename.replace('0630', quarter)
                         alt_filepath = os.path.join(self.post_market_data_dir, alt_filename)
                         if os.path.exists(alt_filepath):
                             df = self._load_csv_safe(alt_filepath)
                             if df is not None:
                                 self.post_market_data[key] = df
                                 # print(f"  - 已加载盘后数据: {alt_filename}")
                             break # 找到一个就够了
                # else:
                #      print(f"  - 盘后数据文件未找到: {filename}") # 注释掉以保持日志简洁

    def _load_yesterday_data(self):
        """
        加载前一交易日的所有相关数据（包括时间线和盘后数据）。
        """
        # 1. 加载昨日的时间线数据
        if not os.path.exists(self.yesterday_data_dir):
            print(f"  - 警告: 昨日时间线数据目录未找到: {self.yesterday_data_dir}")
        else:
            for filename in os.listdir(self.yesterday_data_dir):
                file_type = self._classify_file(filename)
                if file_type == 'other': continue

                filepath = os.path.join(self.yesterday_data_dir, filename)
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.yesterday_data['intraday'][file_type].append(df)
            print(f"  - 昨日时间线数据加载完成。")

        # 2. 加载昨日的盘后数据
        yesterday_date_numeric = os.path.basename(self.yesterday_post_market_data_dir)

        post_market_files = {
            'lhb_detail': '龙虎榜详情_当日.csv',
            'cxg': '创新高_前100.csv',
            'lxsz': '连续上涨_全部.csv',
            'stock_news': '涨停股池个股新闻.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{yesterday_date_numeric}.csv'
        }
        if not os.path.exists(self.yesterday_post_market_data_dir):
            print(f"  - 警告: 昨日盘后数据目录未找到: {self.yesterday_post_market_data_dir}")
            return

        for key, filename in post_market_files.items():
            filepath = os.path.join(self.yesterday_post_market_data_dir, filename)
            if os.path.exists(filepath):
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.yesterday_data['post_market'][key] = df
        print(f"  - 昨日盘后数据加载完成。")

    def _get_target_list(self):
        """
        读取收盘后的涨停股池文件，确定要分析的目标列表。
        增强版：支持多种文件格式和命名规则。
        """
        # 寻找最接近收盘的涨停池文件
        eod_zt_files = []

        # 按时间倒序查找涨停池文件
        for time_key in sorted(self.all_day_data.keys(), reverse=True):
            if 'zt_pool' in self.all_day_data[time_key]:
                # 过滤掉previous_zt_pool.csv，优先选择有"涨停原因"列的文件
                valid_dfs = []
                for df in self.all_day_data[time_key]['zt_pool']:
                    # 检查是否包含关键列，用于判断是否为有效的涨停股池文件
                    if any(col in df.columns for col in ['涨停原因', '代码', '名称', 'code', 'name']):
                        # 排除昨日涨停股池文件
                        if not any(keyword in str(df.columns) for keyword in ['previous', '昨日', '前日']):
                            valid_dfs.append(df)

                if valid_dfs:
                    # 优先选择包含"涨停原因"列的文件
                    priority_dfs = [df for df in valid_dfs if '涨停原因' in df.columns]
                    eod_zt_files = priority_dfs if priority_dfs else valid_dfs
                    break

        # 如果动态数据里没有，尝试从盘后静态数据加载
        if not eod_zt_files and 'zt_pool' in self.post_market_data:
            df_eod_zt = self.post_market_data['zt_pool']
        elif eod_zt_files:
            df_eod_zt = eod_zt_files[0]
        else:
            # 如果还是没有找到，尝试直接从文件系统查找最新的涨停股池文件
            df_eod_zt = self._find_latest_zt_pool_file()
            if df_eod_zt is None:
                return []

        # 标准化列名以兼容不同来源
        column_mapping = {
            "代码": "code", "股票代码": "code", "Code": "code",
            "名称": "name", "股票名称": "name", "Name": "name",
            "涨跌幅": "change_pct", "涨幅": "change_pct", "Change": "change_pct",
            "连板数": "consecutive_boards", "连板": "consecutive_boards",
            "炸板次数": "broken_times", "炸板": "broken_times",
            "最后封板时间": "final_seal_time", "封板时间": "final_seal_time",
            "所属行业": "industry", "行业": "industry", "Industry": "industry"
        }

        df_eod_zt.rename(columns=column_mapping, inplace=True)

        return df_eod_zt.to_dict('records')

    def _find_latest_zt_pool_file(self):
        """
        直接从文件系统查找最新的涨停股池文件。
        支持多种文件命名格式。
        """
        if not os.path.exists(self.data_dir):
            return None

        zt_pool_files = []

        # 遍历目录中的所有文件
        for filename in os.listdir(self.data_dir):
            if not filename.endswith('.csv'):
                continue

            # 检查是否为涨停股池文件（排除昨日涨停股池）
            is_zt_pool = False
            is_previous = False

            # 检查是否为涨停股池文件
            if any(pattern in filename for pattern in ['zt_pool_', '_zt_pool.csv', 'zt_pool.csv']):
                is_zt_pool = True

            # 检查是否为昨日涨停股池文件
            if any(pattern in filename for pattern in ['previous', 'zt_pool_previous_', '昨日', '前日']):
                is_previous = True

            # 只处理涨停股池文件，排除昨日涨停股池
            if is_zt_pool and not is_previous:
                filepath = os.path.join(self.data_dir, filename)
                try:
                    # 获取文件修改时间
                    mtime = os.path.getmtime(filepath)
                    zt_pool_files.append((filename, filepath, mtime))
                except:
                    continue

        if not zt_pool_files:
            return None

        # 按修改时间排序，选择最新的文件
        zt_pool_files.sort(key=lambda x: x[2], reverse=True)
        latest_file = zt_pool_files[0][1]

        # 尝试加载最新的涨停股池文件
        df = self._load_csv_safe(latest_file)
        if df is not None and not df.empty:
            print(f"  - 找到收盘后涨停股池文件: {os.path.basename(latest_file)}")
            return df

        return None

    def _generate_yesterday_insights(self, archive):
        """
        为单个今日涨停股，生成基于昨日数据的回溯分析。
        """
        code = archive['info']['code']
        name = archive['info']['name']
        industry = archive['info']['industry']
        insights = []

        # 1. 昨日资金流入分析
        if self.yesterday_data['intraday']['individual_flow']:
            try:
                all_yesterday_flows = pd.concat(self.yesterday_data['intraday']['individual_flow'])
                stock_flow = all_yesterday_flows[all_yesterday_flows['代码'].astype(str).str.zfill(6) == code]
                if not stock_flow.empty:
                    net_inflow_col = next((col for col in stock_flow.columns if '净流入' in col), None)
                    if net_inflow_col and not stock_flow.empty and stock_flow[net_inflow_col].iloc[0] > 0:
                        insights.append(f"- **资金异动**: 昨日个股资金呈净流入状态。")
            except Exception:
                pass  # Concat may fail if DFs are empty or have different columns

        # 2. 昨日板块热度分析
        if self.yesterday_data['intraday']['sector_flow']:
            best_rank = 101
            for df in self.yesterday_data['intraday']['sector_flow']:
                if '名称' in df.columns:
                    sector_rank_info = df[df['名称'] == industry]
                    if not sector_rank_info.empty:
                        rank = sector_rank_info.index[0] + 1
                        if rank < best_rank:
                            best_rank = rank
            if best_rank <= 20:
                insights.append(f"- **板块热度**: 所属行业 '{industry}' 昨日资金排名曾进入前 **{best_rank}** 名。")

        # 3. 昨日盘口信号
        if self.yesterday_data['intraday']['movers']:
            for df in self.yesterday_data['intraday']['movers']:
                if '名称' in df.columns and name in df['名称'].values:
                    insights.append(f"- **盘口信号**: 昨日曾出现'大买盘'或'大笔买入'信号。")
                    break

        # 4. 昨日龙虎榜
        if 'lhb_detail' in self.yesterday_data['post_market']:
            lhb_df = self.yesterday_data['post_market']['lhb_detail']
            if '代码' in lhb_df.columns and code in lhb_df['代码'].astype(str).str.zfill(6).values:
                insights.append(f"- **龙虎榜**: **昨日**已登上龙虎榜，市场关注度较高。")

        if insights:
            return "**昨日关键信号回溯**:\n" + "\n".join(insights)
        return ""

    def _generate_post_market_insights(self, archive):
        """
        为单个股票生成基于盘后数据的深度复盘洞察。
        """
        code = archive['info']['code']
        insights = []

        # 1. 龙虎榜分析
        if 'lhb_detail' in self.post_market_data:
            lhb_df = self.post_market_data['lhb_detail']
            if '代码' in lhb_df.columns:
                stock_lhb = lhb_df[lhb_df['代码'].astype(str).str.zfill(6) == code]
                if not stock_lhb.empty:
                    reason = stock_lhb['上榜原因'].iloc[0] if '上榜原因' in stock_lhb.columns else "未知原因"
                    if '净买额' in stock_lhb.columns:
                        net_buy = stock_lhb['净买额'].iloc[0]
                        insights.append(
                            f"- **龙虎榜**: 该股因\"{reason}\"登上龙虎榜，总净买额为 **{self._format_money(net_buy)}**。")
                    else:
                        insights.append(
                            f"- **龙虎榜**: 该股因\"{reason}\"登上龙虎榜。")

        # 2. 大宗交易分析
        if 'bulk_trades' in self.post_market_data:
            bulk_df = self.post_market_data['bulk_trades']
            if '代码' in bulk_df.columns:
                stock_bulk = bulk_df[bulk_df['代码'].astype(str).str.zfill(6) == code]
                if not stock_bulk.empty:
                    total_amount = stock_bulk['成交额'].sum()
                    insights.append(
                        f"- **大宗交易**: 当日发生 **{len(stock_bulk)}** 笔大宗交易，总成交额 **{self._format_money(total_amount)}**。")

        # 3. 技术指标信号分析
        tech_signals = []
        tech_map = {
            'cxg': '创月新高', 'lxsz': '连续上涨', 'cxfl': '持续放量',
            'ljqs': '量价齐升', 'xstp_5ma': '向上突破5日线', 'xstp_20ma': '向上突破20日线'
        }
        for key, signal_name in tech_map.items():
            if key in self.post_market_data:
                df = self.post_market_data[key]
                if '代码' in df.columns and code in df['代码'].astype(str).str.zfill(6).values:
                    tech_signals.append(signal_name)
        if tech_signals:
            insights.append(f"- **技术信号**: 触发了 **{', '.join(tech_signals)}** 等技术指标信号。")

        # 4. 业绩与公告分析
        if 'performance_report' in self.post_market_data:
            perf_df = self.post_market_data['performance_report']
            if '股票代码' in perf_df.columns:
                stock_perf = perf_df[perf_df['股票代码'].astype(str).str.zfill(6) == code]
                if not stock_perf.empty and '净利润同比增长' in stock_perf.columns:
                    yoy_growth = stock_perf['净利润同比增长'].iloc[0]
                    if pd.notna(yoy_growth):
                        insights.append(f"- **业绩驱动**: 根据最新财报，公司净利润同比增长 **{yoy_growth:.2f}%**。")

        if 'performance_forecast' in self.post_market_data:
            fore_df = self.post_market_data['performance_forecast']
            if '股票代码' in fore_df.columns:
                stock_fore = fore_df[fore_df['股票代码'].astype(str).str.zfill(6) == code]
                if not stock_fore.empty and '业绩变动' in stock_fore.columns:
                    forecast_desc = stock_fore['业绩变动'].iloc[0]
                    insights.append(f"- **业绩预告**: 公司发布业绩预告，预计“{forecast_desc}”。")

        if 'stock_news' in self.post_market_data:
            news_df = self.post_market_data['stock_news']
            if '股票代码' in news_df.columns:
                stock_news = news_df[news_df['股票代码'].astype(str).str.zfill(6) == code]
                if not stock_news.empty and '新闻标题' in stock_news.columns:
                    latest_news_title = stock_news['新闻标题'].iloc[0]
                    insights.append(f"- **近期新闻**: 关联到近期新闻：“{latest_news_title[:40]}...”。")

        if insights:
            return "**盘后数据深度复盘**:\n" + "\n".join(insights)
        return ""

    def _build_stock_info_map(self, target_stocks):
        """
        从全天数据中构建 股票代码 -> {行业, 概念} 的映射，用于快速查找。
        """
        # 尝试从概念板块文件构建
        for time_key in sorted(self.all_day_data.keys()):
            if 'concept_flow' in self.all_day_data[time_key]:
                for df in self.all_day_data[time_key]['concept_flow']:
                    if '相关个股' in df.columns and '概念名称' in df.columns:
                        for _, row in df.iterrows():
                            concept_name = row['概念名称']
                            stocks = str(row['相关个股']).split(',')
                            for stock_str in stocks[:3]:
                                stock_code = stock_str.split(' ')[0]
                                if stock_code.isdigit():
                                    if stock_code not in self.stock_info_map:
                                        self.stock_info_map[stock_code] = {'industry': '未知', 'concepts': set()}
                                    self.stock_info_map[stock_code]['concepts'].add(concept_name)

        # 从最终涨停池补充行业信息
        for stock in target_stocks:
            code = str(stock.get('code', '')).zfill(6)
            industry = stock.get('industry', '未知')
            if code in self.stock_info_map:
                if self.stock_info_map[code]['industry'] == '未知':
                    self.stock_info_map[code]['industry'] = industry
            else:
                self.stock_info_map[code] = {'industry': industry, 'concepts': set()}

    def _build_timeline_archive(self, stock):
        """
        为单个涨停股构建回溯时间线档案。(v1.6 终极增强版：整合全数据源、丰富细节并追踪主线)
        """
        archive = {
            'info': {
                'code': str(stock.get('code', '')).zfill(6),
                'name': stock.get('name'),
                'industry': self.stock_info_map.get(str(stock.get('code', '')).zfill(6), {}).get('industry', '未知'),
                'concepts': list(self.stock_info_map.get(str(stock.get('code', '')).zfill(6), {}).get('concepts', [])),
            },
            'status': {
                'change_pct': stock.get('change_pct'),
                'consecutive_boards': stock.get('consecutive_boards'),
                'broken_times': stock.get('broken_times'),
                'final_seal_time': stock.get('final_seal_time')
            },
            'timeline': [],
            'signals_triggered': set()
        }

        sorted_times = sorted(self.all_day_data.keys())

        raw_time_obj = stock.get('final_seal_time')
        if pd.isna(raw_time_obj):
            raw_time_str = '15:00:00'
        else:
            raw_time_str = str(raw_time_obj)

        try:
            if ':' in raw_time_str:
                final_seal_time_dt = datetime.strptime(f"{self.analysis_date} {raw_time_str}", "%Y-%m-%d %H:%M:%S")
            else:
                formatted_time = ''.join(filter(str.isdigit, raw_time_str)).zfill(6)
                final_seal_time_dt = datetime.strptime(f"{self.analysis_date} {formatted_time}", "%Y-%m-%d %H%M%S")
        except (ValueError, TypeError):
            final_seal_time_dt = datetime.strptime(f"{self.analysis_date} 15:00:00", "%Y-%m-%d %H:%M:%S")

        # --- 核心修改：使用更精细的排名追踪器 ---
        best_rank_trackers = {
            'individual': 101,
            'sector': 101,
            'concepts': defaultdict(lambda: 101)
        }
        mainline_signal_triggered = set()
        last_zt_status = None

        for time_key in sorted_times:
            if time_key == "全天数据": continue

            try:
                current_time_dt = datetime.strptime(f"{self.analysis_date} {time_key}:00", "%Y-%m-%d %H:%M:%S")
                if current_time_dt > final_seal_time_dt:
                    break
            except ValueError:
                continue

            time_point_events = []
            data_at_time = self.all_day_data.get(time_key, {})

            # 1. 资金异动 (个股) - 整合多源数据并丰富细节
            all_individual_flow_dfs = []
            for key in ['individual_flow', 'fund_flow_rank', 'fund_flow']:
                if key in data_at_time:
                    all_individual_flow_dfs.extend(data_at_time[key])

            if all_individual_flow_dfs:
                processed_dfs = []
                for df in all_individual_flow_dfs:
                    temp_df = df.copy()
                    code_col = next((c for c in temp_df.columns if c in ['代码', '股票代码']), None)
                    name_col = next((c for c in temp_df.columns if c in ['名称', '股票简称']), None)
                    if code_col and name_col:
                        temp_df.rename(columns={code_col: '代码', name_col: '名称'}, inplace=True)
                        processed_dfs.append(temp_df)

                if processed_dfs:
                    combined_df = pd.concat(processed_dfs).drop_duplicates(subset=['代码']).reset_index(drop=True)
                    combined_df['代码'] = combined_df['代码'].astype(str).str.zfill(6)
                    stock_flow_series = combined_df[combined_df['代码'] == archive['info']['code']]

                    if not stock_flow_series.empty:
                        stock_flow = stock_flow_series.iloc[0]
                        rank = stock_flow.name + 1
                        if rank < best_rank_trackers['individual']:
                            best_rank_trackers['individual'] = rank
                            tier = 10 if rank <= 10 else 20 if rank <= 20 else 50 if rank <= 50 else 100

                            details = []
                            # 提取核心指标
                            net_inflow_col = next((c for c in stock_flow.index if '净流入' in c or '净额' in c), None)
                            if net_inflow_col: details.append(f"净流入{self._format_money(stock_flow[net_inflow_col])}")

                            # 提取补充指标
                            extra_cols = {
                                '今日涨跌幅': '涨跌幅', '今日主力净流入-净占比': '主力净占比',
                                '今日超大单净流入-净额': '超大单净额', '今日超大单净流入-净占比': '超大单净占比'
                            }
                            for col, alias in extra_cols.items():
                                if col in stock_flow and pd.notna(stock_flow[col]):
                                    value = stock_flow[col]
                                    if '额' in col:
                                        details.append(f"{alias}:{self._format_money(value)}")
                                    elif '占比' in col or '涨跌幅' in col:
                                        try:
                                            # 尝试转换为浮点数
                                            numeric_value = float(value)
                                            details.append(f"{alias}:{numeric_value:.2f}%")
                                        except (ValueError, TypeError):
                                            # 如果转换失败，直接使用原值
                                            details.append(f"{alias}:{value}%")

                            event = f"个股资金流入排名进入前{tier} (第{rank}名)。[{' | '.join(details)}]"
                            time_point_events.append(event)
                            archive['signals_triggered'].add(f"资金排名前{tier}")

            # 2. 板块/概念排名追踪
            stock_industry = archive['info']['industry']
            if stock_industry != '未知' and 'sector_flow' in data_at_time:
                df = pd.concat(data_at_time['sector_flow']).drop_duplicates(subset=['名称']).reset_index(drop=True)
                sector_flow = df[df['名称'] == stock_industry]
                if not sector_flow.empty:
                    rank = sector_flow.index[0] + 1
                    if rank < best_rank_trackers['sector']:
                        best_rank_trackers['sector'] = rank
                        event = f"所属行业 '{stock_industry}' 资金排名升至第{rank}名。"
                        time_point_events.append(event)
                        if rank <= 5: archive['signals_triggered'].add("行业排名前5")

            if archive['info']['concepts'] and 'concept_flow' in data_at_time:
                df = pd.concat(data_at_time['concept_flow']).drop_duplicates(subset=['名称']).reset_index(drop=True)
                for concept in archive['info']['concepts']:
                    concept_flow = df[df['名称'] == concept]
                    if not concept_flow.empty:
                        rank = concept_flow.index[0] + 1
                        if rank < best_rank_trackers['concepts'][concept]:
                            best_rank_trackers['concepts'][concept] = rank
                            event = f"所属概念 '{concept}' 资金排名升至第{rank}名。"
                            time_point_events.append(event)
                            if rank <= 5: archive['signals_triggered'].add("概念排名前5")

            # 3. 主线攻击 (`main_fund_flow`) 分析
            if 'main_fund_flow' in data_at_time:
                df = pd.concat(data_at_time['main_fund_flow']).drop_duplicates()
                if '主线名称' in df.columns:
                    mainline_names = set(df['主线名称'].dropna().tolist())
                    stock_boards = set([archive['info']['industry']] + archive['info']['concepts'])
                    hit_boards = stock_boards & mainline_names
                    for board in hit_boards:
                        if board not in mainline_signal_triggered:
                            event = f"所属板块/概念 '{board}' 成为资金攻击主线。"
                            time_point_events.append(event)
                            mainline_signal_triggered.add(board)

            # 4. 微观信号 (大买盘/大笔买入) - 增强版支持更多数据源
            # 检查传统的movers数据
            if 'movers' in data_at_time:
                for df in data_at_time['movers']:
                    if '名称' in df.columns and archive['info']['name'] in df['名称'].values:
                        mover_type = "大笔买入" if "大笔买入" in str(df.columns) else "有大买盘"
                        time_point_events.append(f"出现 '{mover_type}' 信号。")
                        archive['signals_triggered'].add("大买盘/大笔买入")
                        break

            # 检查新的大笔买入数据
            if 'big_purchase' in data_at_time:
                for df in data_at_time['big_purchase']:
                    if ('名称' in df.columns and archive['info']['name'] in df['名称'].values) or \
                       ('代码' in df.columns and archive['info']['code'] in df['代码'].astype(str).str.zfill(6).values):
                        # 获取详细信息
                        detail_info = self._extract_mover_details(df, archive['info']['name'], archive['info']['code'])
                        time_point_events.append(f"出现 '大笔买入异动' 信号{detail_info}。")
                        archive['signals_triggered'].add("大买盘/大笔买入")
                        break

            # 检查新的大买盘数据
            if 'big_buy' in data_at_time:
                for df in data_at_time['big_buy']:
                    if ('名称' in df.columns and archive['info']['name'] in df['名称'].values) or \
                       ('代码' in df.columns and archive['info']['code'] in df['代码'].astype(str).str.zfill(6).values):
                        # 获取详细信息
                        detail_info = self._extract_mover_details(df, archive['info']['name'], archive['info']['code'])
                        time_point_events.append(f"出现 '大买盘异动' 信号{detail_info}。")
                        archive['signals_triggered'].add("大买盘/大笔买入")
                        break

            # 5. 其他信号 (新闻, 板块异动, 封板/炸板)
            if 'news' in data_at_time:
                for df in data_at_time['news']:
                    if '标题' in df.columns and "新闻催化" not in archive['signals_triggered']:
                        if df['标题'].str.contains(archive['info']['name'], na=False).any():
                            time_point_events.append(
                                f"相关新闻发布: '{df[df['标题'].str.contains(archive['info']['name'], na=False)]['标题'].iloc[0][:30]}...'")
                            archive['signals_triggered'].add("新闻催化")

            # 5. 涨停/炸板状态追踪 - 增强版支持更多数据源
            current_zt_status = None

            # 检查涨停状态
            if 'zt_pool' in data_at_time:
                for df in data_at_time['zt_pool']:
                    if '代码' in df.columns and archive['info']['code'] in df['代码'].astype(str).str.zfill(6).values:
                        current_zt_status = "涨停"
                        break

            # 检查炸板状态
            if 'zb_pool' in data_at_time:
                for df in data_at_time['zb_pool']:
                    if ('代码' in df.columns and archive['info']['code'] in df['代码'].astype(str).str.zfill(6).values) or \
                       ('名称' in df.columns and archive['info']['name'] in df['名称'].values):
                        current_zt_status = "炸板"
                        break

            # 状态变化事件
            if current_zt_status and current_zt_status != last_zt_status:
                if last_zt_status is None and current_zt_status == "涨停":
                    event_text = "首次封板成功。"
                elif last_zt_status == "炸板" and current_zt_status == "涨停":
                    event_text = "再次封板成功。"
                elif last_zt_status == "涨停" and current_zt_status == "炸板":
                    event_text = "封板被打开。"
                else:
                    event_text = f"状态变化: {last_zt_status} -> {current_zt_status}"

                time_point_events.append(event_text)
                last_zt_status = current_zt_status

            if time_point_events:
                archive['timeline'].append({'time': time_key, 'events': list(set(time_point_events))})

        return archive

    def _quantify_signals(self, archives):
        """
        量化统计所有涨停股触发的信号，计算成功率。
        新增信号组合统计。
        """
        signal_counts = defaultdict(int)
        total_stocks = len(archives)

        # 定义信号组合 (使用更泛化的信号名称)
        signal_combinations = {
            "A级信号 (早盘强攻)": {"资金排名前20", "行业排名前5", "大买盘/大笔买入"},
            "B级信号 (板块驱动)": {"行业排名前5", "概念排名前5"},
            "C级信号 (事件驱动)": {"新闻催化", "大买盘/大笔买入"},
            "D级信号 (资金异动)": {"资金排名前50", "大买盘/大笔买入"}
        }

        for archive in archives:
            # 创建一个泛化的信号集合用于组合匹配
            generic_signals = set()
            for s in archive['signals_triggered']:
                if s.startswith("资金排名前"):
                    # 将 "资金排名前10", "资金排名前20" 等统一归类
                    tier = int(re.search(r'\d+', s).group())
                    if tier <= 10: generic_signals.add("资金排名前10")
                    if tier <= 20: generic_signals.add("资金排名前20")
                    if tier <= 50: generic_signals.add("资金排名前50")
                else:
                    generic_signals.add(s)

            # 统计单个信号
            for signal in generic_signals:
                signal_counts[signal] += 1

            # 统计信号组合
            for combo_name, combo_signals in signal_combinations.items():
                if combo_signals.issubset(generic_signals):
                    signal_counts[combo_name] += 1

        signal_stats = {}
        for signal, count in signal_counts.items():
            signal_stats[signal] = {
                'count': count,
                'rate': (count / total_stocks) * 100 if total_stocks > 0 else 0
            }

        # 按成功率排序
        sorted_stats = sorted(signal_stats.items(), key=lambda item: item[1]['rate'], reverse=True)
        return dict(sorted_stats)

    def _classify_patterns(self, archives):
        """
        根据时间线索为每个涨停股打上模式标签。
        """
        for archive in archives:
            timeline = archive['timeline']
            status = archive['status']
            info = archive['info']

            is_early_strong = False
            is_sector_hot_first = False
            has_news = "新闻催化" in archive['signals_triggered']

            if timeline:
                first_signal_time = timeline[0]['time']
                if datetime.strptime(first_signal_time, "%H:%M").time() < datetime.strptime("09:45", "%H:%M").time():
                    if "资金排名前10" in archive['signals_triggered'] or "资金排名前20" in archive['signals_triggered']:
                        is_early_strong = True

                # 检查板块是否先于个股热门
                first_stock_signal_time = "23:59"
                first_board_signal_time = "23:59"
                for event in timeline:
                    for desc in event['events']:
                        if "个股资金" in desc and event['time'] < first_stock_signal_time:
                            first_stock_signal_time = event['time']
                        if ("所属行业" in desc or "所属概念" in desc) and event['time'] < first_board_signal_time:
                            first_board_signal_time = event['time']

                if first_board_signal_time < first_stock_signal_time:
                    is_sector_hot_first = True

            # --- 核心修改开始 ---
            # 应用规则打标签
            # 确保在比较时间之前，将 final_seal_time 转换为字符串并进行健壮性解析
            final_seal_time_str = str(status.get('final_seal_time', '150000'))
            try:
                if ':' in final_seal_time_str:
                    final_seal_time_obj = datetime.strptime(final_seal_time_str, "%H:%M:%S").time()
                else:
                    final_seal_time_obj = datetime.strptime(final_seal_time_str.zfill(6), "%H%M%S").time()
            except ValueError:
                final_seal_time_obj = datetime.strptime("15:00:00", "%H:%M:%S").time()

            if status.get('broken_times', 0) > 3:
                archive['pattern'] = "模式E (多空博弈型)"
                archive['pattern_reason'] = f"全天炸板{status.get('broken_times', 0)}次，多空争夺激烈。"
            elif final_seal_time_obj > datetime.strptime("14:00:00", "%H:%M:%S").time():
                archive['pattern'] = "模式D (尾盘偷袭型)"
                archive['pattern_reason'] = f"于{status.get('final_seal_time')}偷袭封板，需关注次日持续性。"
            elif is_early_strong:
                archive['pattern'] = "模式A (龙头驱动型)"
                archive['pattern_reason'] = f"早盘即展现强势资金攻击，带动板块情绪。"
            elif is_sector_hot_first:
                archive['pattern'] = "模式B (板块轮动型)"
                archive['pattern_reason'] = f"所属板块/概念先于个股走强，后被资金挖掘。"
            elif has_news:
                archive['pattern'] = "模式C (消息催化型)"
                archive['pattern_reason'] = f"受相关消息刺激，资金快速反应。"
            else:
                archive['pattern'] = "模式F (综合驱动型)"
                archive['pattern_reason'] = f"由多种因素综合作用，无特别突出特征。"
            # --- 核心修改结束 ---

        return archives

    def _generate_report(self, archives, signal_stats, target_stocks):
        """
        生成最终的Markdown格式报告 (v3.6 联动增强版)。
        """
        # --- 报告头部 ---
        report = f"# {self.analysis_date} 涨停股异动分析报告（侦探版 v3.5）\n\n"

        # --- 1. 概述 ---
        pattern_counts = defaultdict(int)
        for archive in archives:
            pattern_counts[archive['pattern']] += 1

        first_board_count = sum(1 for stock in target_stocks if stock.get('consecutive_boards') == 1)
        consecutive_board_count = len(target_stocks) - first_board_count

        report += "## 一、市场概况\n"
        report += f"本报告基于{self.analysis_date}的市场数据，通过**结果驱动的回溯分析法**，追踪**{len(target_stocks)}只**最终涨停股票的盘中异动线索，并结合盘后及昨日数据进行深度复盘，揭示涨停的关键信号和驱动模式。\n\n"

        report += "### 当日涨停股票统计\n"
        report += f"- **总数**: {len(target_stocks)}只涨停股票\n"
        report += f"- **首板**: {first_board_count}只 ({first_board_count / len(target_stocks):.1%})\n"
        report += f"- **连板**: {consecutive_board_count}只 ({consecutive_board_count / len(target_stocks):.1%})\n\n"

        report += "### 当日涨停模式分布\n"
        if pattern_counts:
            for pattern, count in sorted(pattern_counts.items(), key=lambda item: item[1], reverse=True):
                report += f"- **{pattern}**: {count} 只\n"
        report += "\n"

        # --- 2. 宏观板块分析 ---
        report += "## 二、宏观板块分析\n"
        last_time_key = None
        for t in sorted([k for k in self.all_day_data.keys() if k != '全天数据'], reverse=True):
            if self.all_day_data.get(t, {}).get('sector_flow'):
                last_time_key = t
                break
        if last_time_key:
            eod_sector_flow_dfs = self.all_day_data.get(last_time_key, {}).get('sector_flow', [])
            if eod_sector_flow_dfs:
                eod_sector_flow = pd.concat(eod_sector_flow_dfs).drop_duplicates(subset=['名称']).reset_index(drop=True)
                net_inflow_col_name = next((col for col in eod_sector_flow.columns if '净流入' in col), 'net_inflow')
                eod_sector_flow.rename(columns={net_inflow_col_name: 'net_inflow', '名称': 'name'}, inplace=True)
                eod_sector_flow['net_inflow'] = pd.to_numeric(eod_sector_flow['net_inflow'], errors='coerce').fillna(0)
                top_sectors = eod_sector_flow.nlargest(10, 'net_inflow')

                report += "### 资金流入前十板块与涨停股关联\n"
                report += "| 板块名称 | 净流入(亿元) | 涨停股数量 | 代表股票 |\n"
                report += "|---|---|---|---|\n"
                zt_by_industry = defaultdict(list)
                for s in target_stocks:
                    industry = s.get('industry', s.get('所属行业'))
                    if industry:
                        zt_by_industry[industry].append(s.get('name', s.get('名称')))
                for _, row in top_sectors.iterrows():
                    sector_name = row['name']
                    zt_in_sector = zt_by_industry.get(sector_name, [])
                    zt_names = [name for name in zt_in_sector if name is not None]
                    report += f"| {sector_name} | {row['net_inflow'] / 1e8:.2f} | {len(zt_names)} | {', '.join(zt_names[:2])} |\n"
                report += "\n**关键发现**:\n"
                if not top_sectors.empty and zt_by_industry:
                    flow_rank_1_name = top_sectors.iloc[0]['name']
                    flow_rank_1_zt_count = len(zt_by_industry.get(flow_rank_1_name, []))
                    zt_count_rank = sorted(zt_by_industry.items(), key=lambda item: len(item[1]), reverse=True)
                    zt_rank_1_name = zt_count_rank[0][0]
                    zt_rank_1_count = len(zt_count_rank[0][1])
                    if flow_rank_1_zt_count < zt_rank_1_count and flow_rank_1_name != zt_rank_1_name:
                        report += f"1. **资金与热点分化**: **{flow_rank_1_name}**板块资金流入最大，但涨停股仅**{flow_rank_1_zt_count}**只。而**{zt_rank_1_name}**板块涨停股数量最多（**{zt_rank_1_count}**只），显示出更强的板块效应。\n"
                    else:
                        report += f"1. **市场焦点明确**: **{flow_rank_1_name}**板块不仅资金流入最多，涨停家数也最多（或接近最多），成为市场绝对主线。\n"
        report += "\n"

        # --- 3. 板块内涨停联动推演 ---
        report += "## 三、板块内涨停联动推演\n"

        def _get_follower_signals_after_leader(leader_seal_time_str, follower_archive):
            """
            在龙头涨停后的特定时间窗口内，为跟风股捕捉联动信号。(v1.2 增强版：增加个股排名分析)
            """
            signals = set()
            try:
                start_dt = datetime.strptime(f"{self.analysis_date} {leader_seal_time_str}", "%Y-%m-%d %H:%M:%S")
                # 联动窗口期设置为龙头涨停后15分钟
                end_dt = start_dt + timedelta(minutes=15)
            except ValueError:
                return ""

            follower_code = follower_archive['info']['code']
            follower_name = follower_archive['info']['name']
            follower_industry = follower_archive['info']['industry']

            for time_key in sorted(self.all_day_data.keys()):
                try:
                    event_dt = datetime.strptime(f"{self.analysis_date} {time_key}:00", "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    continue

                if start_dt < event_dt <= end_dt:
                    data_at_time = self.all_day_data.get(time_key, {})

                    # a. 板块资金排名
                    if 'sector_flow' in data_at_time:
                        all_dfs = data_at_time['sector_flow']
                        if all_dfs:
                            combined_df = pd.concat(all_dfs).drop_duplicates(subset=['名称']).reset_index(drop=True)
                            sector_info = combined_df[combined_df['名称'] == follower_industry]
                            if not sector_info.empty and sector_info.index[0] < 10:
                                rank = sector_info.index[0] + 1
                                signals.add(f"板块排名升至第{rank}名({time_key})")

                    # b. 个股资金排名
                    if 'individual_flow' in data_at_time:
                        all_dfs = data_at_time['individual_flow']
                        if all_dfs:
                            for df_ind in all_dfs:
                                if '代码' in df_ind.columns:
                                    df_ind['代码'] = df_ind['代码'].astype(str)
                            combined_df = pd.concat(all_dfs).drop_duplicates(subset=['代码']).reset_index(drop=True)
                            combined_df['代码'] = combined_df['代码'].str.zfill(6)
                            stock_info = combined_df[combined_df['代码'] == follower_code]
                            if not stock_info.empty and stock_info.index[0] < 50:
                                rank = stock_info.index[0] + 1
                                signals.add(f"个股排名冲入前50({time_key}, 第{rank}名)")

                    # c. 大买盘信号
                    if 'movers' in data_at_time:
                        for df in data_at_time['movers']:
                            if '名称' in df.columns and follower_name in df['名称'].values:
                                signals.add(f"出现大买盘({time_key})")

            if signals:
                return f" (联动信号: {', '.join(sorted(list(signals)))})"
            return ""

        industry_to_archives = defaultdict(list)
        for archive in archives:
            industry = archive['info'].get('industry')
            if industry and industry != '未知':
                industry_to_archives[industry].append(archive)

        def get_formatted_seal_time(archive):
            seal_time_obj = archive['status'].get('final_seal_time')
            if pd.notna(seal_time_obj):
                raw_time_str = str(seal_time_obj)
                if ':' in raw_time_str:
                    try:
                        dt_obj = datetime.strptime(raw_time_str, "%H:%M:%S")
                        return dt_obj.strftime("%H:%M:%S")
                    except ValueError:
                        return "15:00:00"
                else:
                    s = ''.join(filter(str.isdigit, raw_time_str)).zfill(6)
                    if len(s) == 6:
                        return f"{s[0:2]}:{s[2:4]}:{s[4:6]}"
            # 如果没有封板时间，则使用第一个“首次封板”事件的时间
            for event in archive.get('timeline', []):
                if '首次封板成功' in ' '.join(event.get('events', [])):
                    time_str = event['time']
                    return f"{time_str}:00"
            return "09:25:00"

        hot_industries = sorted(
            [(industry, stock_archives) for industry, stock_archives in industry_to_archives.items() if
             len(stock_archives) > 1],
            key=lambda item: len(item[1]),
            reverse=True
        )

        if not hot_industries:
            report += "本日市场未出现明显的板块内涨停联动效应（即单个板块内涨停股数量均少于2只）。\n\n"
        else:
            for industry, stock_archives in hot_industries[:3]:
                report += f"### {industry} 板块 ({len(stock_archives)}只涨停)\n"
                sorted_stocks_in_industry = sorted(stock_archives, key=get_formatted_seal_time)
                report += "**联动路径**:\n"
                leader_seal_time = get_formatted_seal_time(sorted_stocks_in_industry[0])

                for i, stock_archive in enumerate(sorted_stocks_in_industry):
                    seal_time = get_formatted_seal_time(stock_archive)
                    role = "龙头率先" if i == 0 else "跟风助攻"
                    follower_signals = ""
                    if i > 0:
                        follower_signals = _get_follower_signals_after_leader(leader_seal_time, stock_archive)

                    report += f"- **{seal_time}**: **{stock_archive['info']['name']}** ({role})封板。{follower_signals}\n"

                leader = sorted_stocks_in_industry[0]
                followers_names = [s['info']['name'] for s in sorted_stocks_in_industry[1:]]
                if followers_names:
                    report += f"**简析**: **{leader['info']['name']}** 的率先涨停，对板块情绪起到了关键的带动作用。随后 **{', '.join(followers_names)}** 等个股的跟进，进一步强化了板块的赚钱效应。\n\n"
        report += "\n"

        # --- 4. 补充深度分析 ---
        report += "## 四、补充深度分析\n"
        all_board_changes = []
        for time_key in sorted(self.all_day_data.keys()):
            if 'board_changes' in self.all_day_data.get(time_key, {}):
                for df in self.all_day_data[time_key]['board_changes']:
                    all_board_changes.append(df)
        if all_board_changes:
            df_board_changes = pd.concat(all_board_changes)
            if '板块名称' in df_board_changes.columns:
                top_changed_boards = df_board_changes['板块名称'].value_counts().nlargest(2)
                report += "### 板块异动监控\n"
                for board, count in top_changed_boards.items():
                    report += f"- **'{board}'**板块全天异动 **{count}** 次，位居前列，是日内反复活跃的核心方向。\n"

        all_main_flow = []
        for time_key in sorted(self.all_day_data.keys()):
            if "09:30" <= time_key <= "09:45":
                if 'main_fund_flow' in self.all_day_data.get(time_key, {}):
                    for df in self.all_day_data[time_key]['main_fund_flow']:
                        df['time'] = time_key
                        all_main_flow.append(df)
        if all_main_flow:
            df_main_flow_raw = pd.concat(all_main_flow, ignore_index=True)
            if '主线名称' in df_main_flow_raw.columns:
                df_main_flow = df_main_flow_raw.drop_duplicates(subset=['time', '主线名称'])
                report += "\n### 主线资金流向时间线 (09:30-09:45)\n"
                last_mainline = ""
                for _, row in df_main_flow.iterrows():
                    if pd.notna(row['主线名称']) and row['主线名称'] != last_mainline:
                        report += f"- **{row['time']}**: **{row['主线名称']}** 成为资金攻击主线\n"
                        last_mainline = row['主线名称']
        report += "\n"

        # --- 5. 全部涨停股复盘 ---
        report += "## 五、全部涨停股复盘\n"
        sorted_archives = sorted(archives, key=lambda x: (x['pattern'], -float(x['status'].get('change_pct', 0) or 0)))
        for archive in sorted_archives:
            report += f"### {archive['info']['name']} ({archive['info']['code']}) - {archive['pattern']}\n"
            report += f"**收盘状态**: {float(archive['status']['change_pct'] or 0):.2f}%涨停, {archive['status']['consecutive_boards']}连板, {archive['status']['broken_times']}次炸板, 最后封板: {archive['status']['final_seal_time']}\n"
            report += f"**所属**: [{archive['info']['industry']}], [{', '.join(archive['info']['concepts'][:3])}]\n"

            if archive['timeline']:
                report += "**盘中关键线索**:\n"
                for event in archive['timeline']:
                    report += f"- **{event['time']}**: {' | '.join(event['events'])}\n"
            else:
                report += "**盘中关键线索**: *盘中无明显异动信号，属于一字板或集合竞价涨停。*\n"

            report += f"**涨停逻辑归因**: \"{archive['pattern_reason']}\"\n"

            yesterday_section = self._generate_yesterday_insights(archive)
            if yesterday_section:
                report += f"{yesterday_section}\n"

            post_market_section = self._generate_post_market_insights(archive)
            if post_market_section:
                report += f"{post_market_section}\n"

            report += "\n---\n\n"

        # --- 6. 时间线关键节点 ---
        report += "## 六、时间线关键节点\n"
        time_slots = {
            "09:30-09:45": "开盘抢筹期", "09:45-10:30": "热点发酵期",
            "10:30-11:30": "上午轮动期", "13:00-14:00": "午后观察期",
            "14:00-15:00": "尾盘突袭期"
        }
        for slot, name in time_slots.items():
            start_str, end_str = slot.split('-')
            events_in_slot = []
            for archive in archives:
                for event in archive['timeline']:
                    if start_str <= event['time'] < end_str:
                        simple_event = re.sub(r'\(.*?\)|\[.*?\]', '', event['events'][0]).strip()
                        events_in_slot.append(f"{archive['info']['name']}({simple_event})")
            report += f"### {slot} ({name})\n"
            if events_in_slot:
                unique_events = list(set(events_in_slot))
                report += f"**重要信号**: {len(unique_events)}个异动信号出现。代表: {', '.join(unique_events[:3])}...\n"
            else:
                report += "**重要信号**: 市场相对平稳。\n"
        report += "\n"

        # --- 7. 核心预警信号总结 ---
        report += "## 七、核心预警信号总结\n"
        report += "通过对全部涨停股的回溯统计，本日最有效的盘中预警信号及组合如下：\n\n"
        report += "| 信号/组合类型 | 触发涨停股数量 | 成功率 | 信号强度评级 |\n"
        report += "|---|---|---|---|\n"
        if signal_stats:
            for signal, stats in signal_stats.items():
                rate = stats.get('rate', 0)
                rating = "A级 (强烈)" if rate > 70 else "B级 (关注)" if rate > 40 else "C级 (参考)"
                report += f"| {signal} | {stats.get('count', 0)} | {rate:.1f}% | {rating} |\n"
        report += "\n"

        # --- 8. 实战监控建议 ---
        report += "## 八、实战监控建议\n"
        report += "基于本日分析，明日盘中应重点监控具备以下特征的异动：\n"
        report += "1. **开盘30分钟内**: 重点监控触发**A级或B级信号组合**的个股，并结合**主线资金流向**判断攻击方向。\n"
        report += "2. **板块效应**: 关注**板块异动监控**和**联动推演**中反复提及的板块，寻找龙头确认后的补涨机会。\n"
        report += "3. **微观信号**: 对于进入观察池的个股，若盘中出现带具体金额的**“大买盘”**信号，是重要的确认信号。\n"
        report += "4. **风险提示**: 注意“模式E (多空博弈型)”和“模式D (尾盘偷袭型)”的涨停股，其持续性存疑，次日风险较高。\n"

        return report

    def _save_report(self, report_content):
        """保存报告到文件"""
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
        filename = os.path.join(OUTPUT_DIR, f"zt_detective_report_{self.analysis_date}.md")
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        print(f"报告已保存到: {filename}")

    # --- 辅助工具函数 ---
    def _extract_time_key(self, filename):
        """从多种格式的文件名中提取时间戳 (HH:MM)"""
        # 格式1: HH-MM_ (确保不匹配日期格式，必须在文件名开头或下划线后)
        match = re.search(r'(?:^|_)(\d{2})-(\d{2})_', filename)
        if match:
            hour, minute = match.groups()
            # 验证是否为有效时间（避免匹配日期）
            if 0 <= int(hour) <= 23 and 0 <= int(minute) <= 59:
                return f"{hour}:{minute}"

        # 格式2: _YYYYMMDD_HHMMSS
        match = re.search(r'_\d{8}_(\d{2})(\d{2})\d{2}\.', filename)
        if match:
            return f"{match.group(1)}:{match.group(2)}"

        # 格式3: _HHMMSS.csv
        match = re.search(r'_(\d{2})(\d{2})\d{2}\.csv$', filename)
        if match:
            return f"{match.group(1)}:{match.group(2)}"

        # 格式4: zt_pool_20250730_HHMMSS.csv
        match = re.search(r'_\d{8}_(\d{2})(\d{2})\d{2}\.csv$', filename)
        if match:
            return f"{match.group(1)}:{match.group(2)}"

        # 格式5: big_purchase_unusual_20250730_HHMMSS.csv
        match = re.search(r'_(\d{2})(\d{2})\d{2}\.csv$', filename)
        if match:
            return f"{match.group(1)}:{match.group(2)}"

        # 格式6: 14-57_zt_pool.csv (直接从文件名提取时间，确保不匹配日期)
        match = re.search(r'^(\d{2})-(\d{2})_', filename)
        if match:
            hour, minute = match.groups()
            # 验证是否为有效时间（避免匹配日期）
            if 0 <= int(hour) <= 23 and 0 <= int(minute) <= 59:
                return f"{hour}:{minute}"

        return None

    def _classify_file(self, filename):
        """根据文件名关键词分类，增强版支持更多文件格式"""
        # 特殊处理：优先检查特定的文件模式

        # 检查涨停股池文件（排除昨日涨停股池）
        if ('zt_pool' in filename and 'previous' not in filename) or filename.endswith('_zt_pool.csv'):
            return 'zt_pool'

        # 检查昨日涨停股池文件
        if 'zt_pool_previous_' in filename or 'previous_zt_pool' in filename:
            return 'zt_pool_previous'

        # 检查跌停股池文件
        if 'dt_pool' in filename:
            return 'dt_pool'

        # 检查炸板股池文件
        if 'zb_pool' in filename:
            return 'zb_pool'

        # 检查大笔买入文件
        if 'big_purchase' in filename:
            return 'big_purchase'

        # 检查大买盘文件
        if 'big_buy' in filename:
            return 'big_buy'

        # 通用模式匹配
        all_patterns = []
        for file_type, patterns in self.file_types.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))

        # 优先匹配更长的、更具体的模式
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

        for pattern, file_type in all_patterns:
            if pattern in filename:
                return file_type

        return 'other'

    def _load_csv_safe(self, filepath):
        """安全加载CSV文件"""
        if not os.path.exists(filepath): return None
        try:
            return pd.read_csv(filepath, encoding='utf-8')
        except:
            try:
                return pd.read_csv(filepath, encoding='gbk')
            except Exception as e:
                # print(f"警告: 无法读取文件 {os.path.basename(filepath)} - {e}")
                return None

    def _format_money(self, amount):
        """格式化金额显示"""
        if not isinstance(amount, (int, float)): return "N/A"
        if abs(amount) >= 1e8:
            return f"{amount / 1e8:.2f}亿"
        elif abs(amount) >= 1e4:
            return f"{amount / 1e4:.0f}万"
        return str(int(amount))

    def _extract_mover_details(self, df, stock_name, stock_code):
        """提取大买盘/大笔买入的详细信息"""
        try:
            # 查找匹配的股票行
            matched_row = None
            if '名称' in df.columns and stock_name in df['名称'].values:
                matched_row = df[df['名称'] == stock_name].iloc[0]
            elif '代码' in df.columns:
                # 确保代码格式一致（6位数字）
                df_codes = df['代码'].astype(str).str.zfill(6)
                stock_code_padded = str(stock_code).zfill(6)
                if stock_code_padded in df_codes.values:
                    matched_row = df[df_codes == stock_code_padded].iloc[0]

            if matched_row is None:
                return ""

            # 解析相关信息列
            if '相关信息' in matched_row and pd.notna(matched_row['相关信息']):
                info_str = str(matched_row['相关信息'])
                # 格式: "手数,价格,涨跌幅,金额"
                parts = info_str.split(',')
                if len(parts) >= 4:
                    try:
                        volume = int(float(parts[0]))  # 手数
                        price = float(parts[1])        # 价格
                        change_pct = float(parts[2])   # 涨跌幅
                        amount = float(parts[3])       # 金额

                        # 格式化显示
                        volume_str = f"{volume:,}手" if volume >= 1000 else f"{volume}手"
                        amount_str = self._format_money(amount)
                        change_str = f"{change_pct:+.2%}" if abs(change_pct) >= 0.001 else "平盘"

                        return f" (价格{price:.2f}元, {volume_str}, {amount_str}, {change_str})"
                    except (ValueError, IndexError):
                        pass

            return ""
        except Exception:
            return ""


if __name__ == "__main__":
    # 使用示例
    detective = ZtDetective(
        analysis_date=ANALYSIS_DATE,
        data_dir=FUND_DATA_DIR,
        post_market_data_dir=POST_MARKET_DATA_DIR
    )
    detective.run_analysis()